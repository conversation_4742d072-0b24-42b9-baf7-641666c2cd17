import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const prompt = `Analyze this product image and generate relevant tags. 
    Focus on:
    - Product type (shoes, sneakers, boots, etc.)
    - Brand (if visible)
    - Colors
    - Style/category
    - Materials (if visible)
    - Any distinctive features
    
    Return only a comma-separated list of relevant tags, no explanations.`;

    const result = await visionModel.generateContent([
      {
        inlineData: {
          data: await getImageAsBase64(imageUrl),
          mimeType: 'image/jpeg'
        }
      },
      prompt
    ]);

    const response = await result.response;
    const tagsText = response.text();
    
    // Parse the comma-separated tags
    const tags = tagsText
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .slice(0, 10); // Limit to 10 tags

    return NextResponse.json({ tags });
  } catch (error) {
    console.error('Error generating image tags:', error);
    return NextResponse.json(
      { error: 'Failed to generate tags', tags: [] },
      { status: 500 }
    );
  }
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
