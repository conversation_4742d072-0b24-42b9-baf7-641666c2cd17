"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import ProductImageGallery from "@/components/products/product-image-gallery";
import AdminBackButton from "@/components/admin/common/admin-back-button";
import ProductImageEnhancer from "@/components/admin/products/ai/ProductImageEnhancer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Sparkles, Zap, Eye } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { Product, User } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { set } from "better-auth";

export default function AdminViewProductPage() {
  const { id } = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<User | null | undefined>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);
      setError("");
      try {
        const res = await fetch(`/api/products/${id}`);
        const data = await res.json();
        const userFromDb = await getCurrentUser();

        setUser(userFromDb);

        if (data.success) {
          setProduct(data.data);
        } else {
          setError(data.error || "Product not found");
        }
      } catch (err) {
        setError("Failed to fetch product");
      } finally {
        setLoading(false);
      }
    };
    if (id) fetchProduct();
  }, [id]);

  if (loading) {
    return (
      <div className="flex w-full h-screen justify-center items-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (user) {
    return (
      <AdminLayout user={user}>
        <div className="max-w-4xl mx-auto py-8 px-4">
          <AdminBackButton href="/admin/products" label="Back to Products" />
          <h1 className="text-3xl font-bold mb-4">Product Details</h1>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <SpinnerCircle4 />
            </div>
          ) : error ? (
            <div className="text-red-600">{error}</div>
          ) : product ? (
            <div className="bg-white border rounded-lg shadow p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-semibold mb-2">{product.name}</h2>
                <p className="text-gray-600 mb-1">Brand: {product.brand}</p>
                <p className="text-gray-600 mb-1">
                  Category: {product.category?.name}
                </p>
                <p className="text-gray-600 mb-1">Price: M{product.price}</p>
                {product.discountedPrice && (
                  <p className="text-gray-600 mb-1">
                    Discounted Price: M{product.discountedPrice}
                  </p>
                )}
                <p className="text-gray-600 mb-1">Stock: {product.stock}</p>
                <p className="text-gray-600 mb-1">
                  Active: {product.isActive ? "Yes" : "No"}
                </p>
                <p className="text-gray-600 mb-1">
                  Created: {new Date(product.createdAt).toLocaleString()}
                </p>
                <p className="text-gray-600 mb-1">
                  Updated: {new Date(product.updatedAt).toLocaleString()}
                </p>
              </div>
              <div className="mb-6">
                <ProductImageGallery
                  images={product.images}
                  productName={product.name}
                />
              </div>

              {/* AI Image Enhancement Section */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-purple-500" />
                    AI Multi-Angle Image Enhancement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">
                          Current images: {product.images.length} |
                          Status: {product.enhancementStatus || 'Not enhanced'} |
                          Quality: {product.qualityScore || 'N/A'}%
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Enhance to get 5-7 multi-angle images with white backgrounds
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={async () => {
                          if (confirm(`Start multi-angle enhancement for "${product.name}"?\n\nThis will:\n• Search for 6-7 different viewing angles\n• Find white background studio shots\n• Apply Rivv logo watermarking\n• Update the product with enhanced images`)) {
                            try {
                              const button = document.querySelector('button') as HTMLButtonElement;
                              if (button) {
                                button.disabled = true;
                                button.textContent = 'Enhancing...';
                              }

                              const response = await fetch('/api/test-enhancement', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                  originalImageUrl: product.images[0],
                                  productId: product.id,
                                  autoApprove: true
                                })
                              });

                              const data = await response.json();

                              if (data.success) {
                                alert(`Enhancement Successful! 🎉

Original Images: ${data.data.originalImageCount}
New Images: ${data.data.newImageCount}
Images Added: ${data.data.imagesAdded}
Quality Score: ${data.data.confidenceScore}%
Status: ${data.data.enhancementStatus}

The page will refresh to show the new images.`);
                                window.location.reload();
                              } else {
                                alert('Enhancement failed: ' + (data.error || 'Unknown error') + '\n\nDetails: ' + (data.details || 'No additional details'));
                                if (button) {
                                  button.disabled = false;
                                  button.textContent = 'Enhance with Multi-Angle Search';
                                }
                              }
                            } catch (error) {
                              alert('Enhancement failed: ' + error.message);
                              const button = document.querySelector('button') as HTMLButtonElement;
                              if (button) {
                                button.disabled = false;
                                button.textContent = 'Enhance with Multi-Angle Search';
                              }
                            }
                          }
                        }}
                        className="flex items-center gap-2"
                      >
                        <Sparkles className="w-4 h-4" />
                        Enhance with Multi-Angle Search
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => {
                          alert(`Product Enhancement Info:

Current Status: ${product.enhancementStatus || 'Not enhanced'}
Current Images: ${product.images.length}
Quality Score: ${product.qualityScore || 'N/A'}%
Last Enhanced: ${product.lastEnhancedAt ? new Date(product.lastEnhancedAt).toLocaleString() : 'Never'}

The enhancement will:
• Search for 6-7 different angles (front, side, back, top, sole, close-up)
• Find white background studio shots
• Apply Rivv logo watermarking
• Update product with 5-7 high-quality images`);
                        }}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Enhancement Info
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div>
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-gray-700 whitespace-pre-line">
                  {product.description}
                </p>
              </div>
            </div>
          ) : null}
        </div>
      </AdminLayout>
    );
  }

  router.push("/sign-in");
  return (
    <div className="flex justify-center items-center h-40">
      <SpinnerCircle4 />
    </div>
  );
}
