import prisma from '../lib/prisma';
import { processProductImages } from '../utils/image-ai-utils';

async function main() {
  // Get only first 3 products for testing
  const products = await prisma.product.findMany({
    take: 3
  });
  console.log(`Found ${products.length} products for testing.`);

  for (const product of products) {
    if (!product.images || product.images.length === 0) {
      console.log(`Skipping product ${product.id} (${product.name}) - no images.`);
      continue;
    }
    const originalImageUrl = product.images[0];
    try {
      console.log(`Processing product ${product.id} (${product.name})...`);
      console.log(`Original image URL: ${originalImageUrl}`);
      
      const processed = await processProductImages({
        productName: product.name,
        brand: product.brand,
        colorway: '', // Optionally extract from description or add logic
        originalImageUrl
      });
      
      if (processed.images && processed.images.length > 0) {
        console.log(`✅ Successfully processed ${processed.images.length} images for product ${product.id}`);
        console.log('Generated images:', processed.images);
        
        // Uncomment this to actually update the database:
        // await prisma.product.update({
        //   where: { id: product.id },
        //   data: {
        //     images: processed.images.map(i => i.url)
        //   }
        // });
        
      } else {
        console.log(`⚠️ No enhanced images found for product ${product.id}.`);
      }
    } catch (err) {
      console.error(`❌ Error processing product ${product.id}:`, err);
    }
  }
  console.log('Test complete.');
}

main().catch((err) => {
  console.error('Fatal error:', err);
  process.exit(1);
});
