import {
<<<<<<< HEAD
  AlertCircle,
  History,
  Loader2,
=======
  AlertTriangle,
  ArrowRight,
  Check,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Circle,
  Copy,
  CreditCard,
  File,
  FileText,
  HelpCircle,
  Image,
  Laptop,
  Loader2,
  Moon,
  MoreVertical,
  Package,
  Package2,
  PanelLeft,
  Plus,
  Search,
  Settings,
  SunMedium,
  Trash,
  User,
  X,
  Eye,
  EyeOff,
  ShoppingCart,
  Heart,
  Star,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  Clock,
  MapPin,
  Mail,
  Phone,
  Edit,
  Save,
  Download,
  Upload,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  DollarSign,
  Users,
  ShoppingBag,
  Tag,
  Percent,
  History,
  AlertCircle,
  CheckCircle,
  Info,
  XCircle,
>>>>>>> ****************************************
  type LucideIcon,
} from "lucide-react"

export type Icon = LucideIcon

export const Icons = {
<<<<<<< HEAD
  alertCircle: AlertCircle,
  history: History,
  spinner: Loader2,
} as const
=======
  logo: Package2,
  close: X,
  spinner: Loader2,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  chevronUp: ChevronUp,
  chevronDown: ChevronDown,
  trash: Trash,
  post: FileText,
  page: File,
  media: Image,
  settings: Settings,
  billing: CreditCard,
  ellipsis: MoreVertical,
  add: Plus,
  warning: AlertTriangle,
  user: User,
  arrowRight: ArrowRight,
  help: HelpCircle,
  pizza: Circle,
  sun: SunMedium,
  moon: Moon,
  laptop: Laptop,
  search: Search,
  check: Check,
  copy: Copy,
  package: Package,
  panelLeft: PanelLeft,
  eye: Eye,
  eyeOff: EyeOff,
  cart: ShoppingCart,
  heart: Heart,
  star: Star,
  filter: Filter,
  sortAsc: SortAsc,
  sortDesc: SortDesc,
  calendar: Calendar,
  clock: Clock,
  mapPin: MapPin,
  mail: Mail,
  phone: Phone,
  edit: Edit,
  save: Save,
  download: Download,
  upload: Upload,
  refresh: RefreshCw,
  trendingUp: TrendingUp,
  trendingDown: TrendingDown,
  barChart: BarChart3,
  pieChart: PieChart,
  dollarSign: DollarSign,
  users: Users,
  shoppingBag: ShoppingBag,
  tag: Tag,
  percent: Percent,
  history: History,
  alertCircle: AlertCircle,
  checkCircle: CheckCircle,
  info: Info,
  xCircle: XCircle,
}
>>>>>>> ****************************************
