import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

export async function POST(request: NextRequest) {
  try {
    const { productName, currentImageUrl } = await request.json();

    if (!productName || !currentImageUrl) {
      return NextResponse.json(
        { error: 'Product name and current image URL are required' },
        { status: 400 }
      );
    }

    // Analyze current image quality
    const prompt = `Analyze this product image for "${productName}". 
    Rate the image quality on a scale of 1-10 and suggest if a better image is needed.
    Consider: clarity, lighting, angle, background, professional appearance.
    
    Respond with just a number (1-10) representing the quality score.`;

    const result = await visionModel.generateContent([
      {
        inlineData: {
          data: await getImageAsBase64(currentImageUrl),
          mimeType: 'image/jpeg'
        }
      },
      prompt
    ]);

    const response = await result.response;
    const qualityText = response.text().trim();
    const qualityScore = parseInt(qualityText.match(/\d+/)?.[0] || '5');

    // If quality is good (7+), no better image needed
    if (qualityScore >= 7) {
      return NextResponse.json({ betterImageUrl: null });
    }

    // For demonstration purposes, we'll return null since we don't have a real image search
    // In a real implementation, this would search for better quality images of the same product
    
    return NextResponse.json({ 
      betterImageUrl: null,
      message: `Current image quality: ${qualityScore}/10. Consider finding a higher quality image.`
    });
  } catch (error) {
    console.error('Error finding better image:', error);
    return NextResponse.json(
      { error: 'Failed to find better image', betterImageUrl: null },
      { status: 500 }
    );
  }
}

async function getImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error fetching image:', error);
    throw error;
  }
}
