const axios = require('axios');
require('dotenv').config();

async function testSearchAPI() {
  console.log('🔍 Testing Google Custom Search API');
  console.log('===================================');

  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;

    if (!apiKey || !searchEngineId) {
      console.error('❌ Missing API credentials');
      return;
    }

    console.log(`🔑 API Key: ${apiKey.substring(0, 10)}...`);
    console.log(`🔍 Search Engine ID: ${searchEngineId}`);

    // Test different search queries
    const queries = [
      'Nike Air Jordan 4 Light blue white floral front view white background',
      'Nike Air Jordan 4 side view white background',
      'Nike Air Jordan 4 white background studio shot',
      'Air Jordan 4 sneaker white background',
      'Nike sneaker white background'
    ];

    for (const query of queries) {
      console.log(`\n🔍 Testing query: "${query}"`);
      
      const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&searchType=image&num=10&imgSize=large&imgType=photo&safe=active&imgColorType=color&imgDominantColor=white`;

      try {
        const response = await axios.get(searchUrl, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; RivvImageEnhancer/1.0)'
          }
        });

        if (response.data.error) {
          console.error(`❌ API Error:`, response.data.error);
          continue;
        }

        if (!response.data.items || response.data.items.length === 0) {
          console.log('📭 No results found');
          continue;
        }

        console.log(`✅ Found ${response.data.items.length} results`);
        
        // Filter results
        const filteredResults = response.data.items.map((item) => ({
          url: item.link,
          width: parseInt(item.image?.width || '0'),
          height: parseInt(item.image?.height || '0'),
          source: item.displayLink || 'google_search'
        })).filter((img) => 
          img.width >= 800 && 
          img.height >= 800 && 
          img.url.match(/\.(jpg|jpeg|png|webp)$/i) &&
          !img.url.includes('data:')
        );

        console.log(`📊 After filtering: ${filteredResults.length} valid images`);
        
        if (filteredResults.length > 0) {
          console.log('🖼️ Sample results:');
          filteredResults.slice(0, 3).forEach((img, i) => {
            console.log(`   ${i + 1}. ${img.url.substring(0, 80)}...`);
            console.log(`      Size: ${img.width}x${img.height}`);
          });
        }

        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        if (error.response) {
          console.error(`❌ HTTP Error: ${error.response.status} ${error.response.statusText}`);
          if (error.response.data) {
            console.error('📄 Error details:', error.response.data);
          }
        } else {
          console.error(`❌ Request Error: ${error.message}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSearchAPI();
