import { NextRequest, NextResponse } from "next/server";
import { enhanceProductImages } from "@/utils/advanced-image-processor";
import prisma from "@/lib/prisma";
import axios from "axios";

/**
 * POST /api/test-enhancement
 * Test endpoint for image enhancement without authentication
 * This is for testing purposes only
 */
export async function POST(request: NextRequest) {
  try {
    const { productId, originalImageUrl, autoApprove = false, testApis = false } = await request.json();

    // If testApis is true, run API diagnostics
    if (testApis) {
      console.log("🔧 Running API diagnostics...");

      // Test Google Custom Search API
      const searchApiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
      const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;

      console.log("🔍 Testing Google Custom Search API...");
      console.log(`API Key: ${searchApiKey ? `${searchApiKey.substring(0, 10)}...` : 'NOT SET'}`);
      console.log(`Search Engine ID: ${searchEngineId || 'NOT SET'}`);

      if (searchApiKey && searchEngineId) {
        try {
          const testSearchUrl = `https://www.googleapis.com/customsearch/v1?key=${searchApiKey}&cx=${searchEngineId}&q=Nike+Air+Force+1&searchType=image&num=1`;
          const searchResponse = await axios.get(testSearchUrl);
          console.log("✅ Google Custom Search API: Working");
          console.log(`📊 Search results: ${searchResponse.data.items?.length || 0} items`);
        } catch (searchError: any) {
          console.error("❌ Google Custom Search API Error:", {
            status: searchError.response?.status,
            statusText: searchError.response?.statusText,
            data: searchError.response?.data
          });
        }
      }

      // Test Google Gemini API
      const geminiApiKey = process.env.GOOGLE_AI_API_KEY;
      console.log("🤖 Testing Google Gemini API...");
      console.log(`API Key: ${geminiApiKey ? `${geminiApiKey.substring(0, 10)}...` : 'NOT SET'}`);

      if (geminiApiKey) {
        try {
          const testGeminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`;
          const geminiResponse = await axios.post(testGeminiUrl, {
            contents: [{
              parts: [{
                text: "Hello, this is a test. Please respond with 'API Working'."
              }]
            }]
          });
          console.log("✅ Google Gemini API: Working");
          console.log(`📝 Response: ${geminiResponse.data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response'}`);
        } catch (geminiError: any) {
          console.error("❌ Google Gemini API Error:", {
            status: geminiError.response?.status,
            statusText: geminiError.response?.statusText,
            data: geminiError.response?.data
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: "API diagnostics completed. Check console for details.",
        diagnostics: {
          searchApi: {
            configured: !!(searchApiKey && searchEngineId),
            apiKey: searchApiKey ? `${searchApiKey.substring(0, 10)}...` : 'NOT SET',
            searchEngineId: searchEngineId || 'NOT SET'
          },
          geminiApi: {
            configured: !!geminiApiKey,
            apiKey: geminiApiKey ? `${geminiApiKey.substring(0, 10)}...` : 'NOT SET'
          }
        }
      });
    }

    if (!productId || !originalImageUrl) {
      return NextResponse.json(
        { success: false, error: "Missing productId or originalImageUrl" },
        { status: 400 }
      );
    }

    console.log(`🧪 Test Enhancement - Product ID: ${productId}`);

    // Get the product details
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        name: true,
        brand: true,
        images: true,
        enhancementStatus: true,
        qualityScore: true
      }
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    console.log(`🔍 Enhancing: ${product.name} (${product.brand})`);
    console.log(`📸 Original images: ${product.images.length}`);

    // Call the enhancement function
    const enhancementResult = await enhanceProductImages(originalImageUrl, productId);

    console.log(`✅ Enhancement result:`, {
      confidence: enhancementResult.confidence_score,
      images: enhancementResult.images.length,
      source: enhancementResult.image_source
    });

    // Update the product with enhanced images
    const updatedImages = enhancementResult.images.map(img => img.url);
    
    await prisma.product.update({
      where: { id: productId },
      data: {
        images: updatedImages,
        enhancementStatus: autoApprove || enhancementResult.confidence_score >= 85 ? 'completed' : 'review_required',
        qualityScore: enhancementResult.confidence_score
      }
    });

    console.log(`📊 Updated product with ${updatedImages.length} images`);

    return NextResponse.json({
      success: true,
      data: {
        originalImageCount: product.images.length,
        newImageCount: updatedImages.length,
        imagesAdded: updatedImages.length - product.images.length,
        confidenceScore: enhancementResult.confidence_score,
        enhancementStatus: autoApprove || enhancementResult.confidence_score >= 85 ? 'completed' : 'review_required',
        imageSource: enhancementResult.image_source,
        processingNotes: enhancementResult.processing_notes,
        images: enhancementResult.images.map(img => ({
          url: img.url,
          angle: img.angle,
          qualityScore: img.qualityScore,
          source: img.source
        }))
      }
    });

  } catch (error) {
    console.error("❌ Test enhancement error:", error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Enhancement failed",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
