import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

interface ImageAnalysis {
  productId: string;
  currentImages: string[];
  qualityScore: number;
  missingAngles: string[];
  suggestedImprovements: string[];
  brandDetected?: string;
  modelDetected?: string;
  colorwayDetected?: string;
}

/**
 * Analyze a single product's images using AI
 */
async function analyzeProductImages(product: any): Promise<ImageAnalysis> {
  console.log(`🔍 Analyzing images for product: ${product.name}`);
  
  const analysis: ImageAnalysis = {
    productId: product.id,
    currentImages: product.images,
    qualityScore: 0,
    missingAngles: [],
    suggestedImprovements: [],
  };

  if (product.images.length === 0) {
    analysis.qualityScore = 0;
    analysis.missingAngles = ['front', 'side', 'back', 'detail'];
    analysis.suggestedImprovements.push('No images found - needs complete image set');
    return analysis;
  }

  try {
    // Analyze the first (primary) image
    const primaryImage = product.images[0];
    console.log(`🤖 Analyzing primary image with AI...`);
    
    const aiAnalysis = await analyzeImageWithAI(primaryImage, product.category?.name);
    
    if (aiAnalysis) {
      // Calculate quality score based on AI analysis
      analysis.qualityScore = calculateQualityScore(aiAnalysis, product.images.length);
      
      // Determine missing angles
      analysis.missingAngles = determineMissingAngles(aiAnalysis, product.images.length);
      
      // Generate improvement suggestions
      analysis.suggestedImprovements = generateImprovementSuggestions(aiAnalysis, product);
    }
  } catch (error) {
    console.error(`❌ Error analyzing product ${product.id}:`, error);
    analysis.suggestedImprovements.push(`Analysis failed: ${error}`);
  }

  return analysis;
}

/**
 * Use AI to analyze image quality and content
 */
async function analyzeImageWithAI(imageUrl: string, category?: string): Promise<any> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/analyze-image`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        imageUrl
      }),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error);
    }

    return {
      qualityScore: data.score * 100, // Convert back to 0-100 scale
      clarity: data.estimatedQuality,
      issues: data.issues,
      suggestions: data.suggestions,
      category: category
    };
  } catch (error) {
    console.error('AI analysis failed:', error);
    return null;
  }
}

/**
 * Calculate quality score based on AI analysis
 */
function calculateQualityScore(aiAnalysis: any, imageCount: number): number {
  let score = 0;
  
  // Use the AI-provided score as the base
  if (aiAnalysis.qualityScore) {
    score = aiAnalysis.qualityScore;
  } else {
    // Fallback scoring if no AI score
    score += Math.min(imageCount * 20, 60); // Max 60 points for 3+ images
  }
  
  // Quality indicators from AI
  if (aiAnalysis.clarity === 'high') score += 15;
  else if (aiAnalysis.clarity === 'medium') score += 10;
  else if (aiAnalysis.clarity === 'low') score += 5;
  
  // Adjust for issues found
  if (aiAnalysis.issues && aiAnalysis.issues.length > 0) {
    score -= Math.min(aiAnalysis.issues.length * 5, 20); // Deduct up to 20 points for issues
  }
  
  return Math.min(Math.max(score, 0), 100); // Ensure score is between 0-100
}

/**
 * Determine what angles are missing
 */
function determineMissingAngles(aiAnalysis: any, imageCount: number): string[] {
  const missingAngles: string[] = [];
  
  // If we have fewer than 4 images, we're likely missing angles
  if (imageCount < 2) {
    missingAngles.push('side', 'detail');
  }
  if (imageCount < 3) {
    missingAngles.push('back');
  }
  if (imageCount < 4) {
    missingAngles.push('sole', 'detail-close-up');
  }
  
  return [...new Set(missingAngles)]; // Remove duplicates
}

/**
 * Generate improvement suggestions
 */
function generateImprovementSuggestions(aiAnalysis: any, product: any): string[] {
  const suggestions: string[] = [];
  
  // Add AI-generated suggestions
  if (aiAnalysis.suggestions && Array.isArray(aiAnalysis.suggestions)) {
    suggestions.push(...aiAnalysis.suggestions);
  }
  
  // Add additional logic-based suggestions
  if (aiAnalysis.clarity === 'low') {
    suggestions.push('Primary image needs higher resolution/clarity');
  }
  
  if (product.images.length < 3) {
    suggestions.push('Add more images showing different angles (minimum 3-4 images recommended)');
  }
  
  // Add issue-based suggestions
  if (aiAnalysis.issues && Array.isArray(aiAnalysis.issues)) {
    aiAnalysis.issues.forEach((issue: string) => {
      if (issue.toLowerCase().includes('lighting')) {
        suggestions.push('Improve lighting - use natural light or professional setup');
      }
      if (issue.toLowerCase().includes('background')) {
        suggestions.push('Use clean white background for better product visibility');
      }
      if (issue.toLowerCase().includes('blur') || issue.toLowerCase().includes('focus')) {
        suggestions.push('Ensure images are sharp and in focus');
      }
    });
  }
  
  return [...new Set(suggestions)]; // Remove duplicates
}

// GET /api/admin/products/analyze-images - Get image analysis for products
export async function GET(request: NextRequest) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const limit = parseInt(searchParams.get('limit') || '10');

    let products;
    
    if (productId) {
      // Analyze specific product
      products = await prisma.product.findMany({
        where: { id: productId, isActive: true },
        include: { category: true },
      });
    } else {
      // Analyze multiple products
      products = await prisma.product.findMany({
        where: { isActive: true },
        include: { category: true },
        take: limit,
        orderBy: { updatedAt: 'desc' },
      });
    }

    if (products.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No products found to analyze'
      }, { status: 404 });
    }

    // Analyze images for each product
    const analyses = [];
    for (const product of products) {
      try {
        const analysis = await analyzeProductImages(product);
        analyses.push(analysis);
        
        // Rate limiting between requests
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`Error analyzing product ${product.id}:`, error);
        analyses.push({
          productId: product.id,
          currentImages: product.images,
          qualityScore: 0,
          missingAngles: ['unknown'],
          suggestedImprovements: [`Analysis failed: ${error}`],
        });
      }
    }

    // Calculate summary statistics
    const summary = {
      totalAnalyzed: analyses.length,
      averageScore: analyses.reduce((sum, a) => sum + a.qualityScore, 0) / analyses.length,
      needingImprovement: analyses.filter(a => a.qualityScore < 70).length,
      withoutImages: analyses.filter(a => a.currentImages.length === 0).length,
    };

    return NextResponse.json({
      success: true,
      data: {
        analyses,
        summary,
      }
    });

  } catch (error) {
    console.error("Error in image analysis API:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to analyze product images"
    }, { status: 500 });
  }
}

// POST /api/admin/products/analyze-images - Trigger batch image analysis
export async function POST(request: NextRequest) {
  try {
    await requireAdmin();

    const body = await request.json();
    const { productIds, analysisType = 'quality_and_angles' } = body;

    let products;
    
    if (productIds && Array.isArray(productIds)) {
      products = await prisma.product.findMany({
        where: { 
          id: { in: productIds },
          isActive: true 
        },
        include: { category: true },
      });
    } else {
      // Analyze all products if no specific IDs provided
      products = await prisma.product.findMany({
        where: { isActive: true },
        include: { category: true },
        take: 50, // Limit to prevent timeout
      });
    }

    if (products.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No products found to analyze'
      }, { status: 404 });
    }

    // Start background analysis
    const analysisPromise = analyzeProductsBatch(products, analysisType);
    
    // Return immediately with job started status
    return NextResponse.json({
      success: true,
      message: `Started analysis of ${products.length} products`,
      data: {
        productsToAnalyze: products.length,
        estimatedTime: `${Math.ceil(products.length * 3 / 60)} minutes`,
      }
    });

  } catch (error) {
    console.error("Error starting image analysis:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to start image analysis"
    }, { status: 500 });
  }
}

/**
 * Background batch analysis function
 */
async function analyzeProductsBatch(products: any[], analysisType: string) {
  console.log(`🚀 Starting background analysis of ${products.length} products`);
  
  const analyses = [];
  
  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    
    try {
      console.log(`📸 Analyzing product ${i + 1}/${products.length}: ${product.name}`);
      const analysis = await analyzeProductImages(product);
      analyses.push(analysis);
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 3000));
      
    } catch (error) {
      console.error(`❌ Failed to analyze ${product.name}:`, error);
      analyses.push({
        productId: product.id,
        currentImages: product.images,
        qualityScore: 0,
        missingAngles: ['unknown'],
        suggestedImprovements: [`Analysis failed: ${error}`],
      });
    }
  }
  
  console.log(`✅ Completed background analysis of ${products.length} products`);
  
  // Here you could save results to database, send email notification, etc.
  const summary = {
    totalAnalyzed: analyses.length,
    averageScore: analyses.reduce((sum, a) => sum + a.qualityScore, 0) / analyses.length,
    needingImprovement: analyses.filter(a => a.qualityScore < 70).length,
  };
  
  console.log('📊 Final Summary:', summary);
  
  return analyses;
}