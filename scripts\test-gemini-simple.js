const { GoogleGenerativeAI } = require('@google/generative-ai');
const axios = require('axios');
require('dotenv').config();

async function testGeminiSimple() {
  console.log('🤖 Testing Gemini Vision API');
  console.log('============================');

  try {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      console.error('❌ GOOGLE_AI_API_KEY not found');
      return;
    }

    console.log(`🔑 API Key: ${apiKey.substring(0, 10)}...`);

    // Initialize Gemini
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Test with a simple text prompt first
    console.log('\n📝 Testing text-only prompt...');
    const textResult = await model.generateContent('Hello, please respond with "Gemini is working!"');
    const textResponse = await textResult.response;
    console.log('✅ Text response:', textResponse.text());

    // Test with a simple image URL
    console.log('\n🖼️ Testing with image...');
    const imageUrl = 'https://utfs.io/f/9WixNlVtj4JybEUBSSo4gMcdHsrQnmpoKyLwDGUI2CkhFOfN';
    
    console.log(`📥 Fetching image from: ${imageUrl}`);
    const response = await fetch(imageUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64 = buffer.toString('base64');

    console.log(`📊 Image size: ${buffer.length} bytes`);
    console.log(`📄 Base64 length: ${base64.length} characters`);

    // Detect MIME type
    const header = buffer.subarray(0, 4);
    let mimeType = 'image/jpeg'; // default
    if (header[0] === 0xFF && header[1] === 0xD8) {
      mimeType = 'image/jpeg';
    } else if (header[0] === 0x89 && header[1] === 0x50) {
      mimeType = 'image/png';
    }

    console.log(`📄 Detected MIME type: ${mimeType}`);

    const prompt = `Analyze this product image and tell me what you see. Just describe the product in simple terms.`;

    console.log('🚀 Sending to Gemini...');
    const result = await model.generateContent([
      { text: prompt },
      {
        inlineData: {
          mimeType: mimeType,
          data: base64
        }
      }
    ]);

    const visionResponse = await result.response;
    const description = visionResponse.text();

    console.log('✅ Vision response:', description);

    // Now test with the actual product identification prompt
    console.log('\n🎯 Testing product identification...');
    const productPrompt = `Analyze this product image and extract detailed metadata. Focus on identifying:

1. Product type (sneaker, hoodie, cap, shirt, pants, jacket, bag, accessory, etc.)
2. Brand name (look for logos, text, distinctive design elements)
3. Specific model name or product line
4. Colorway/color scheme description
5. Any visible SKU, product codes, or identifying numbers
6. Overall confidence in identification

Return ONLY a valid JSON object with this exact structure:
{
  "productType": "sneaker|hoodie|cap|shirt|pants|jacket|bag|accessory|other",
  "brand": "exact brand name (Nike, Adidas, Puma, etc.) or 'unknown'",
  "modelName": "specific model name or 'unknown'",
  "colorway": "detailed color description",
  "sku": "product code if visible or null",
  "confidence": 85
}

Be very precise with brand identification. Look for logos, text, and distinctive design patterns.`;

    const productResult = await model.generateContent([
      { text: productPrompt },
      {
        inlineData: {
          mimeType: mimeType,
          data: base64
        }
      }
    ]);

    const productResponse = await productResult.response;
    const productText = productResponse.text();

    console.log('📝 Raw product response:', productText);

    // Try to parse as JSON
    try {
      const cleanedText = productText.replace(/```json\n?|\n?```/g, '').trim();
      const metadata = JSON.parse(cleanedText);
      console.log('✅ Parsed metadata:', metadata);
    } catch (parseError) {
      console.error('❌ Failed to parse as JSON:', parseError.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    if (error.message?.includes('Provided image is not valid')) {
      console.error('🖼️ The image format or data is invalid');
    }
  }
}

testGeminiSimple();
