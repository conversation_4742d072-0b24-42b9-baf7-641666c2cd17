'use client';

import { useState, useEffect } from 'react';
<<<<<<< HEAD
import { getActiveCategories } from '@/actions/categoryActions';
=======
import { getCategories } from '@/actions/categoryActions';
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2

interface Category {
  id: string;
  name: string;
  description: string | null;
  image: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  _count: {
    products: number;
  };
}

<<<<<<< HEAD
interface UseCategoriesReturn {
  data: Category[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useCategories(): UseCategoriesReturn {
=======
interface UseCategoriesOptions {
  search?: string;
}

export function useCategories(options: UseCategoriesOptions = {}) {
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2
  const [data, setData] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

<<<<<<< HEAD
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getActiveCategories();
      
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch categories');
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);
=======
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const result = await getCategories({
          search: options.search,
        });
        
        if (result.success) {
          setData(result.data || []);
        } else {
          setError(result.error || 'Failed to fetch categories');
        }
      } catch (err) {
        setError('An unexpected error occurred');
        console.error('Error in useCategories:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, [options.search]);
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2

  return {
    data,
    isLoading,
    error,
<<<<<<< HEAD
    refetch: fetchCategories,
=======
    refetch: () => {
      const fetchCategories = async () => {
        try {
          setIsLoading(true);
          setError(null);
          
          const result = await getCategories({
            search: options.search,
          });
          
          if (result.success) {
            setData(result.data || []);
          } else {
            setError(result.error || 'Failed to fetch categories');
          }
        } catch (err) {
          setError('An unexpected error occurred');
          console.error('Error in useCategories refetch:', err);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategories();
    }
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2
  };
}
