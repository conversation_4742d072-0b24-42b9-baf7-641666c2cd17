import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // For now, we'll do a simple search based on product names and categories
    // In a full implementation, this would use image embeddings for similarity search
    
    // Get some sample products from the database
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        images: {
          isEmpty: false
        }
      },
      select: {
        id: true,
        name: true,
        images: true
      },
      take: 10,
      orderBy: {
        createdAt: 'desc'
      }
    });

    // For demonstration, return products with a mock similarity score
    const similarProducts = products
      .filter(product => product.images && Array.isArray(product.images) && product.images.length > 0)
      .map(product => ({
        id: product.id,
        name: product.name,
        similarity: Math.random() * 0.5 + 0.5, // Mock similarity between 0.5-1.0
        image: Array.isArray(product.images) ? product.images[0] : product.images
      }))
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 5);

    return NextResponse.json({ similarProducts });
  } catch (error) {
    console.error('Error finding similar products:', error);
    return NextResponse.json(
      { error: 'Failed to find similar products', similarProducts: [] },
      { status: 500 }
    );
  }
}
