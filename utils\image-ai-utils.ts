// Client-side image AI utilities
// This file contains types and client-safe functions only
// Server-side processing is handled by API routes

export type ImageEmbedding = number[];

export type ProductImage = {
  id: string;
  url: string;
  embedding?: ImageEmbedding;
  metadata: Record<string, any>;
};

export interface ImageAnalysis {
  score: number;
  issues: string[];
  suggestions: string[];
  estimatedQuality: 'low' | 'medium' | 'high';
}

export interface SimilarProduct {
  id: string;
  name: string;
  similarity: number;
  image: string;
}

/**
 * Client-safe function to process product images via API
 */
export async function processProductImages(params: {
  productName: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<{
  images: Array<{ angle: string; url: string }>;
  images_source: string;
  metadata: Record<string, any>;
}> {
  try {
<<<<<<< HEAD
    // @ts-ignore - Google AI API type mismatch
    const result = await embeddingModel.embedContent({
      content: {
        parts: [
          { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } },
          { text: 'Generate an embedding for visual similarity search' }
        ],
        role: 'user'
=======
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/process-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('Failed to process images');
    }

    return await response.json();
  } catch (error) {
    console.error('Error processing images:', error);
    // Return fallback
    return {
      images: [{ angle: 'main', url: params.originalImageUrl }],
      images_source: 'original',
      metadata: {
        error: 'Processing failed, using original image',
        processed_at: new Date().toISOString()
      }
    };
  }
}

/**
 * Analyze image quality via API
 */
export async function analyzeImageQuality(imageUrl: string): Promise<ImageAnalysis> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/analyze-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to analyze image');
    }

    return await response.json();
  } catch (error) {
    console.error('Error analyzing image:', error);
    return {
      score: 0.5,
      issues: ['Analysis failed'],
      suggestions: ['Please try again'],
      estimatedQuality: 'medium'
    };
  }
}

/**
 * Detect image defects via API
 */
export async function detectImageDefects(imageUrl: string): Promise<string[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/detect-defects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to detect defects');
    }

    const result = await response.json();
    return result.defects || [];
  } catch (error) {
    console.error('Error detecting defects:', error);
    return [];
  }
}

/**
 * Find similar products via API
 */
export async function findSimilarProducts(imageUrl: string): Promise<SimilarProduct[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/find-similar`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to find similar products');
    }

    const result = await response.json();
    return result.similarProducts || [];
  } catch (error) {
    console.error('Error finding similar products:', error);
    return [];
  }
}

/**
 * Find better quality image via API
 */
export async function findBetterQualityImage(productName: string, currentImageUrl: string): Promise<string | null> {
  try {
    const response = await fetch('/api/admin/products/find-better-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ productName, currentImageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to find better image');
    }

    const result = await response.json();
    return result.betterImageUrl || null;
  } catch (error) {
    console.error('Error finding better image:', error);
    return null;
  }
}

/**
 * Optimize image via API
 */
export async function optimizeImage(imageUrl: string): Promise<string> {
  try {
    const response = await fetch('/api/admin/products/optimize-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

<<<<<<< HEAD
    // Enhance (resize, white background)
    let sharpImg = sharp(imageBuffer).resize(1200, 1200, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 1 } });
    imageBuffer = await sharpImg.png().toBuffer();

    // Overlay logo watermark
    const logoPath = path.join(process.cwd(), 'public', 'logo.png');
    if (fs.existsSync(logoPath)) {
      const logoBuffer = fs.readFileSync(logoPath);
      // Create a smaller, semi-transparent logo for watermarking
      const logoResized = await sharp(logoBuffer)
        .resize(80, 80)
        .png()
        .toBuffer();

      // Position logo in bottom-right corner with some padding
      imageBuffer = await sharp(imageBuffer)
        .composite([
          {
            input: logoResized,
            top: 1100, // Bottom area
            left: 1100, // Right area
            blend: 'over'
          }
        ])
        .png()
        .toBuffer();
=======
    if (!response.ok) {
      throw new Error('Failed to optimize image');
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2
    }

    const result = await response.json();
    return result.optimizedImageUrl || imageUrl;
  } catch (error) {
    console.error('Error optimizing image:', error);
    return imageUrl;
  }
}

/**
 * Generate image tags via API
 */
export async function generateImageTags(imageUrl: string): Promise<string[]> {
  try {
    const response = await fetch('/api/admin/products/generate-tags', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate tags');
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Error generating image tags:', error);
    return [];
  }
}

/**
 * Enhance image quality via API
 */
export async function enhanceImageQuality(imageUrl: string): Promise<string> {
  try {
    const response = await fetch('/api/admin/products/enhance-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to enhance image');
    }

    const result = await response.json();
    return result.enhancedImageUrl || imageUrl;
  } catch (error) {
    console.error('Error enhancing image:', error);
    return imageUrl;
  }
}

/**
 * Generate alternative product images via API
 */
export async function generateAlternativeImages(productInfo: {
  name: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<string[]> {
  try {
    const response = await fetch('/api/admin/products/generate-alternatives', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productInfo),
    });

    if (!response.ok) {
      throw new Error('Failed to generate alternative images');
    }

    const result = await response.json();
    return result.alternativeImages || [];
  } catch (error) {
    console.error('Error generating alternative images:', error);
    return [];
  }
}

/**
 * Extract product information from image via API
 */
export async function extractProductInfo(imageUrl: string): Promise<{
  brand?: string;
  model?: string;
  colorway?: string;
  category?: string;
  confidence: number;
}> {
  try {
    const response = await fetch('/api/admin/products/extract-info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to extract product info');
    }

    const result = await response.json();
    return result.productInfo || { confidence: 0 };
  } catch (error) {
    console.error('Error extracting product info:', error);
    return { confidence: 0 };
  }
}

/**
 * Validate product image via API
 */
export async function validateProductImage(imageUrl: string, productType: string): Promise<{
  isValid: boolean;
  issues: string[];
  score: number;
}> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/validate-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl, productType }),
    });

<<<<<<< HEAD
// Helper function to enhance image quality using Sharp
async function enhanceImageQuality(imageUrl: string): Promise<string | null> {
  try {
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    let imageBuffer = Buffer.from(response.data);

    const maxSize = parseInt(process.env.IMAGE_MAX_SIZE || '1200');
    const quality = parseInt(process.env.IMAGE_ENHANCEMENT_QUALITY || '90');

    // Enhance image: resize, sharpen, optimize
    const enhancedBuffer = await sharp(imageBuffer)
      .resize(maxSize, maxSize, {
        fit: 'inside',
        withoutEnlargement: true,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .sharpen(1, 1, 0.5) // Mild sharpening
      .jpeg({ quality, progressive: true })
      .toBuffer();

    // Upload enhanced image
    const fileName = `enhanced_${uuidv4()}.jpg`;
    const enhancedUrl = await uploadToS3(enhancedBuffer, fileName, 'image/jpeg');

    return enhancedUrl;
  } catch (error) {
    console.error('Error enhancing image quality:', error);
    return null;
  }
}

// Helper function to search for images online using Google Custom Search API
async function searchImagesOnline(query: string): Promise<Array<{url: string; width: number; height: number; source?: string}>> {
  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;
    const limit = parseInt(process.env.SEARCH_RESULTS_LIMIT || '20');

    if (!apiKey || !searchEngineId) {
      console.warn('Google Custom Search API not configured');
      return [];
    }

    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&searchType=image&num=${Math.min(limit, 10)}&imgSize=large&imgType=photo&safe=active`;

    const response = await axios.get(searchUrl);

    if (!response.data.items) {
      return [];
    }

    return response.data.items.map((item: any) => ({
      url: item.link,
      width: parseInt(item.image?.width || '0'),
      height: parseInt(item.image?.height || '0'),
      source: item.displayLink || 'google_search'
    })).filter((img: any) => img.width > 300 && img.height > 300); // Filter for decent quality

  } catch (error) {
    console.error('Error searching for images:', error);
    return [];
  }
}

export {
  generateImageEmbedding
};

export type {
  ProductImage,
  ImageEmbedding
};
=======
    if (!response.ok) {
      throw new Error('Failed to validate image');
    }

    const result = await response.json();
    return result.validation || { isValid: false, issues: ['Validation failed'], score: 0 };
  } catch (error) {
    console.error('Error validating image:', error);
    return { isValid: false, issues: ['Validation failed'], score: 0 };
  }
}
>>>>>>> 026ec502c39cff49247e48083a314e313ec2ced2
