// Client-side image AI utilities
// This file contains types and client-safe functions only
// Server-side processing is handled by API routes

export type ImageEmbedding = number[];

export type ProductImage = {
  id: string;
  url: string;
  embedding?: ImageEmbedding;
  metadata: Record<string, any>;
};

export interface ImageAnalysis {
  score: number;
  issues: string[];
  suggestions: string[];
  estimatedQuality: 'low' | 'medium' | 'high';
}

export interface SimilarProduct {
  id: string;
  name: string;
  similarity: number;
  image: string;
}

/**
 * Client-safe function to process product images via API
 */
export async function processProductImages(params: {
  productName: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<{
  images: Array<{ angle: string; url: string }>;
  images_source: string;
  metadata: Record<string, any>;
}> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/process-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('Failed to process images');
    }

    return await response.json();
  } catch (error) {
    console.error('Error processing images:', error);
    // Return fallback
    return {
      images: [{ angle: 'main', url: params.originalImageUrl }],
      images_source: 'original',
      metadata: {
        error: 'Processing failed, using original image',
        processed_at: new Date().toISOString()
      }
    };
  }
}

/**
 * Analyze image quality via API
 */
export async function analyzeImageQuality(imageUrl: string): Promise<ImageAnalysis> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/analyze-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to analyze image');
    }

    return await response.json();
  } catch (error) {
    console.error('Error analyzing image:', error);
    return {
      score: 0.5,
      issues: ['Analysis failed'],
      suggestions: ['Please try again'],
      estimatedQuality: 'medium'
    };
  }
}

/**
 * Detect image defects via API
 */
export async function detectImageDefects(imageUrl: string): Promise<string[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/detect-defects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to detect defects');
    }

    const result = await response.json();
    return result.defects || [];
  } catch (error) {
    console.error('Error detecting defects:', error);
    return [];
  }
}

/**
 * Find similar products via API
 */
export async function findSimilarProducts(imageUrl: string): Promise<SimilarProduct[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/find-similar`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to find similar products');
    }

    const result = await response.json();
    return result.similarProducts || [];
  } catch (error) {
    console.error('Error finding similar products:', error);
    return [];
  }
}

/**
 * Find better quality image via API
 */
export async function findBetterQualityImage(productName: string, currentImageUrl: string): Promise<string | null> {
  try {
    const response = await fetch('/api/admin/products/find-better-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ productName, currentImageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to find better image');
    }

    const result = await response.json();
    return result.betterImageUrl || null;
  } catch (error) {
    console.error('Error finding better image:', error);
    return null;
  }
}

/**
 * Optimize image via API
 */
export async function optimizeImage(imageUrl: string): Promise<string> {
  try {
    const response = await fetch('/api/admin/products/optimize-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to optimize image');
    }

    const result = await response.json();
    return result.optimizedImageUrl || imageUrl;
  } catch (error) {
    console.error('Error optimizing image:', error);
    return imageUrl; // Return original URL as fallback
  }
}

/**
 * Generate image tags via API
 */
export async function generateImageTags(imageUrl: string): Promise<string[]> {
  try {
    const response = await fetch('/api/admin/products/generate-tags', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate tags');
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Error generating image tags:', error);
    return [];
  }
}

/**
 * Enhance image quality via API
 */
export async function enhanceImageQuality(imageUrl: string): Promise<string> {
  try {
    const response = await fetch('/api/admin/products/enhance-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to enhance image');
    }

    const result = await response.json();
    return result.enhancedImageUrl || imageUrl;
  } catch (error) {
    console.error('Error enhancing image:', error);
    return imageUrl;
  }
}

/**
 * Generate alternative product images via API
 */
export async function generateAlternativeImages(productInfo: {
  name: string;
  brand: string;
  colorway: string;
  originalImageUrl: string;
}): Promise<string[]> {
  try {
    const response = await fetch('/api/admin/products/generate-alternatives', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productInfo),
    });

    if (!response.ok) {
      throw new Error('Failed to generate alternative images');
    }

    const result = await response.json();
    return result.alternativeImages || [];
  } catch (error) {
    console.error('Error generating alternative images:', error);
    return [];
  }
}

/**
 * Extract product information from image via API
 */
export async function extractProductInfo(imageUrl: string): Promise<{
  brand?: string;
  model?: string;
  colorway?: string;
  category?: string;
  confidence: number;
}> {
  try {
    const response = await fetch('/api/admin/products/extract-info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to extract product info');
    }

    const result = await response.json();
    return result.productInfo || { confidence: 0 };
  } catch (error) {
    console.error('Error extracting product info:', error);
    return { confidence: 0 };
  }
}

/**
 * Validate product image via API
 */
export async function validateProductImage(imageUrl: string, productType: string): Promise<{
  isValid: boolean;
  issues: string[];
  score: number;
}> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/validate-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl, productType }),
    });

    if (!response.ok) {
      throw new Error('Failed to validate image');
    }

    const result = await response.json();
    return result.validation || { isValid: false, issues: ['Validation failed'], score: 0 };
  } catch (error) {
    console.error('Error validating image:', error);
    return { isValid: false, issues: ['Validation failed'], score: 0 };
  }
}

/**
 * Generate image embedding via API
 */
export async function generateImageEmbedding(imageUrl: string): Promise<number[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/generate-embedding`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate embedding');
    }

    const result = await response.json();
    return result.embedding || [];
  } catch (error) {
    console.error('Error generating embedding:', error);
    return [];
  }
}
