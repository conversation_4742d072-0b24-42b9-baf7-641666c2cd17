/*
# Product Image Analysis and Enhancement Script

This script analyzes existing product images and suggests better versions from multiple angles.
It uses AI to:
1. Analyze current product images for quality and completeness
2. Generate suggestions for additional angles needed
3. Optionally fetch better quality images from external sources
4. Update product records with improved image sets

## Features
- Quality assessment of existing images
- Multi-angle analysis (front, side, back, detail shots)
- Brand and model detection for better image sourcing
- Batch processing with rate limiting
- Progress tracking and error handling
*/

import { PrismaClient } from '@prisma/client';
import { getProductDescriptionFromImage } from '../utils/replicate';

const prisma = new PrismaClient();

interface ImageAnalysis {
  productId: string;
  currentImages: string[];
  qualityScore: number;
  missingAngles: string[];
  suggestedImprovements: string[];
  brandDetected?: string;
  modelDetected?: string;
  colorwayDetected?: string;
}

interface ImageSuggestion {
  url: string;
  angle: string;
  qualityScore: number;
  description: string;
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Rate limiter to respect Google AI API limits (15 RPM)
 */
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests = 14; // Stay slightly under 15 RPM limit
  private readonly timeWindow = 60000; // 1 minute in milliseconds

  async waitIfNeeded(): Promise<void> {
    const now = Date.now();
    
    // Remove requests older than 1 minute
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    // If we're at the limit, wait until we can make another request
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.timeWindow - (now - oldestRequest) + 1000; // Add 1 second buffer
      console.log(`⏳ Rate limit reached. Waiting ${Math.ceil(waitTime / 1000)} seconds...`);
      await sleep(waitTime);
      return this.waitIfNeeded(); // Recursive call to check again
    }
    
    // Add current request to tracker
    this.requests.push(now);
    
    // Add a small delay between requests to be extra safe
    await sleep(4500); // 4.5 seconds between requests ensures ~13 RPM max
  }
}

const rateLimiter = new RateLimiter();

/**
 * Analyze a single product's images using AI
 */
async function analyzeProductImages(product: any): Promise<ImageAnalysis> {
  console.log(`🔍 Analyzing images for product: ${product.name}`);
  
  const analysis: ImageAnalysis = {
    productId: product.id,
    currentImages: product.images,
    qualityScore: 0,
    missingAngles: [],
    suggestedImprovements: [],
  };

  if (product.images.length === 0) {
    analysis.qualityScore = 0;
    analysis.missingAngles = ['front', 'side', 'back', 'detail'];
    analysis.suggestedImprovements.push('No images found - needs complete image set');
    return analysis;
  }

  try {
    // Analyze the first (primary) image
    const primaryImage = product.images[0];
    console.log(`🤖 Analyzing primary image with AI...`);
    
    const aiAnalysis = await analyzeImageWithAI(primaryImage, product.category?.name);
    
    if (aiAnalysis) {
      analysis.brandDetected = aiAnalysis.brand;
      analysis.modelDetected = aiAnalysis.model;
      analysis.colorwayDetected = aiAnalysis.colorway;
      
      // Calculate quality score based on AI analysis
      analysis.qualityScore = calculateQualityScore(aiAnalysis, product.images.length);
      
      // Determine missing angles
      analysis.missingAngles = determineMissingAngles(aiAnalysis, product.images.length);
      
      // Generate improvement suggestions
      analysis.suggestedImprovements = generateImprovementSuggestions(aiAnalysis, product);
    }
  } catch (error) {
    console.error(`❌ Error analyzing product ${product.id}:`, error);
    analysis.suggestedImprovements.push(`Analysis failed: ${error}`);
  }

  return analysis;
}

/**
 * Use AI to analyze image quality and content with proper rate limiting and retry logic
 */
async function analyzeImageWithAI(imageUrl: string, category?: string, retryCount = 0): Promise<any> {
  const maxRetries = 3;
  
  try {
    // Wait for rate limiter approval
    await rateLimiter.waitIfNeeded();
    
    console.log(`🤖 Making API call to analyze image (attempt ${retryCount + 1})...`);
    
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/products/analyze-image`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        imageUrl
      }),
    });

    if (!response.ok) {
      if (response.status === 429) {
        // Rate limit hit - exponential backoff
        const backoffTime = Math.pow(2, retryCount) * 30000; // 30s, 60s, 120s
        console.log(`⏳ Rate limit hit (429). Backing off for ${backoffTime / 1000} seconds...`);
        await sleep(backoffTime);
        
        if (retryCount < maxRetries) {
          return analyzeImageWithAI(imageUrl, category, retryCount + 1);
        } else {
          throw new Error(`Rate limit exceeded after ${maxRetries} retries`);
        }
      }
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error);
    }

    console.log(`✅ Successfully analyzed image`);
    return {
      qualityScore: data.score * 100, // Convert back to 0-100 scale
      clarity: data.estimatedQuality,
      issues: data.issues,
      suggestions: data.suggestions,
      category: category
    };
  } catch (error) {
    if (retryCount < maxRetries && (
      error.message.includes('429') || 
      error.message.includes('quota') ||
      error.message.includes('Rate limit')
    )) {
      const backoffTime = Math.pow(2, retryCount) * 30000; // Exponential backoff
      console.log(`❌ API error, retrying in ${backoffTime / 1000} seconds...`);
      await sleep(backoffTime);
      return analyzeImageWithAI(imageUrl, category, retryCount + 1);
    }
    
    console.error('AI analysis failed after retries:', error);
    return null;
  }
}

/**
 * Calculate quality score based on AI analysis
 */
function calculateQualityScore(aiAnalysis: any, imageCount: number): number {
  let score = 0;
  
  // Use the AI-provided score as the base
  if (aiAnalysis.qualityScore) {
    score = aiAnalysis.qualityScore;
  } else {
    // Fallback scoring if no AI score
    score += Math.min(imageCount * 20, 60); // Max 60 points for 3+ images
  }
  
  // Quality indicators from AI
  if (aiAnalysis.clarity === 'high') score += 15;
  else if (aiAnalysis.clarity === 'medium') score += 10;
  else if (aiAnalysis.clarity === 'low') score += 5;
  
  // Adjust for issues found
  if (aiAnalysis.issues && aiAnalysis.issues.length > 0) {
    score -= Math.min(aiAnalysis.issues.length * 5, 20); // Deduct up to 20 points for issues
  }
  
  return Math.min(Math.max(score, 0), 100); // Ensure score is between 0-100
}

/**
 * Determine what angles are missing
 */
function determineMissingAngles(aiAnalysis: any, imageCount: number): string[] {
  const requiredAngles = ['front', 'side', 'back', 'detail'];
  const missingAngles: string[] = [];
  
  // If we have fewer than 4 images, we're likely missing angles
  if (imageCount < 2) {
    missingAngles.push('side', 'detail');
  }
  if (imageCount < 3) {
    missingAngles.push('back');
  }
  if (imageCount < 4) {
    missingAngles.push('sole', 'detail-close-up');
  }
  
  // AI-detected missing angles
  if (aiAnalysis.missingAngles) {
    missingAngles.push(...aiAnalysis.missingAngles);
  }
  
  return [...new Set(missingAngles)]; // Remove duplicates
}

/**
 * Generate improvement suggestions
 */
function generateImprovementSuggestions(aiAnalysis: any, product: any): string[] {
  const suggestions: string[] = [];
  
  // Add AI-generated suggestions
  if (aiAnalysis.suggestions && Array.isArray(aiAnalysis.suggestions)) {
    suggestions.push(...aiAnalysis.suggestions);
  }
  
  // Add additional logic-based suggestions
  if (aiAnalysis.clarity === 'low') {
    suggestions.push('Primary image needs higher resolution/clarity');
  }
  
  if (product.images.length < 3) {
    suggestions.push('Add more images showing different angles (minimum 3-4 images recommended)');
  }
  
  // Add issue-based suggestions
  if (aiAnalysis.issues && Array.isArray(aiAnalysis.issues)) {
    aiAnalysis.issues.forEach((issue: string) => {
      if (issue.toLowerCase().includes('lighting')) {
        suggestions.push('Improve lighting - use natural light or professional setup');
      }
      if (issue.toLowerCase().includes('background')) {
        suggestions.push('Use clean white background for better product visibility');
      }
      if (issue.toLowerCase().includes('blur') || issue.toLowerCase().includes('focus')) {
        suggestions.push('Ensure images are sharp and in focus');
      }
    });
  }
  
  return [...new Set(suggestions)]; // Remove duplicates
}

/**
 * Search for better images using external APIs (placeholder)
 */
async function searchForBetterImages(product: any, analysis: ImageAnalysis): Promise<ImageSuggestion[]> {
  // This would integrate with image search APIs like:
  // - Unsplash API
  // - Pexels API
  // - Google Custom Search API
  // - Brand-specific APIs
  
  console.log(`🔎 Searching for better images for: ${product.brand} ${product.name}`);
  
  // Placeholder implementation
  const suggestions: ImageSuggestion[] = [];
  
  // For now, return empty array - this would be implemented with actual image search
  return suggestions;
}

/**
 * Generate a comprehensive report
 */
function generateReport(analyses: ImageAnalysis[]): void {
  console.log('\n📊 IMAGE ANALYSIS REPORT');
  console.log('========================');
  
  const totalProducts = analyses.length;
  const avgQualityScore = analyses.reduce((sum, a) => sum + a.qualityScore, 0) / totalProducts;
  const productsNeedingImprovement = analyses.filter(a => a.qualityScore < 70).length;
  const productsWithoutImages = analyses.filter(a => a.currentImages.length === 0).length;
  
  console.log(`📈 Overall Statistics:`);
  console.log(`   Total Products Analyzed: ${totalProducts}`);
  console.log(`   Average Quality Score: ${avgQualityScore.toFixed(1)}/100`);
  console.log(`   Products Needing Improvement: ${productsNeedingImprovement} (${((productsNeedingImprovement/totalProducts)*100).toFixed(1)}%)`);
  console.log(`   Products Without Images: ${productsWithoutImages}`);
  
  console.log(`\n🔍 Detailed Analysis:`);
  
  // Group by quality score ranges
  const excellent = analyses.filter(a => a.qualityScore >= 90).length;
  const good = analyses.filter(a => a.qualityScore >= 70 && a.qualityScore < 90).length;
  const fair = analyses.filter(a => a.qualityScore >= 50 && a.qualityScore < 70).length;
  const poor = analyses.filter(a => a.qualityScore < 50).length;
  
  console.log(`   Excellent (90-100): ${excellent} products`);
  console.log(`   Good (70-89): ${good} products`);
  console.log(`   Fair (50-69): ${fair} products`);
  console.log(`   Poor (0-49): ${poor} products`);
  
  // Show top issues
  const allIssues = analyses.flatMap(a => a.suggestedImprovements);
  const issueCount = allIssues.reduce((acc, issue) => {
    acc[issue] = (acc[issue] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  console.log(`\n🚨 Top Issues Found:`);
  Object.entries(issueCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .forEach(([issue, count]) => {
      console.log(`   ${count}x: ${issue}`);
    });
  
  // Show products that need immediate attention
  const criticalProducts = analyses.filter(a => a.qualityScore < 30);
  if (criticalProducts.length > 0) {
    console.log(`\n🚨 Products Needing Immediate Attention:`);
    criticalProducts.forEach(analysis => {
      console.log(`   Product ID: ${analysis.productId} (Score: ${analysis.qualityScore})`);
      analysis.suggestedImprovements.forEach(suggestion => {
        console.log(`     - ${suggestion}`);
      });
    });
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Product Image Analysis Script...');
  console.log('==========================================');
  
  try {
    // Get all active products with images
    const products = await prisma.product.findMany({
      where: { isActive: true },
      include: { category: true },
      orderBy: { createdAt: 'desc' },
    });
    
    console.log(`📦 Found ${products.length} active products to analyze`);
    
    if (products.length === 0) {
      console.log('❌ No products found to analyze');
      return;
    }
    
    const analyses: ImageAnalysis[] = [];
    let processed = 0;
    
    // Process products ONE AT A TIME to respect rate limits
    console.log(`\n📋 Processing products individually with rate limiting...`);
    console.log(`⚠️  This will take approximately ${Math.ceil(products.length * 5 / 60)} minutes due to API rate limits\n`);
    
    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      
      console.log(`\n� Processing product ${i + 1}/${products.length}: ${product.name}`);
      
      try {
        const analysis = await analyzeProductImages(product);
        processed++;
        console.log(`✅ Completed ${product.name} - Score: ${analysis.qualityScore}/100`);
        analyses.push(analysis);
        
        // Show progress
        const progressPercent = Math.round((processed / products.length) * 100);
        console.log(`📊 Progress: ${processed}/${products.length} (${progressPercent}%)`);
        
      } catch (error) {
        console.error(`❌ Failed to analyze ${product.name}:`, error);
        processed++;
        analyses.push({
          productId: product.id,
          currentImages: product.images,
          qualityScore: 0,
          missingAngles: ['unknown'],
          suggestedImprovements: [`Analysis failed: ${error}`],
        });
      }
    }
    
    // Generate and display report
    generateReport(analyses);
    
    // Save analysis results to database for future reference
    await saveAnalysisResults(analyses);
    
    console.log('\n🎉 Image analysis completed successfully!');
    console.log('\n💡 Next Steps:');
    console.log('   1. Review the analysis report above');
    console.log('   2. Focus on products with scores below 70');
    console.log('   3. Upload better images for products without images');
    console.log('   4. Add missing angles for incomplete product sets');
    console.log('   5. Re-run this script after improvements to track progress');
    
  } catch (error) {
    console.error('💥 Script failed with error:', error);
    throw error;
  }
}

/**
 * Save analysis results to database for tracking
 */
async function saveAnalysisResults(analyses: ImageAnalysis[]): Promise<void> {
  try {
    // Create a simple log entry in the database
    // You could extend this to create a dedicated ImageAnalysis model
    const summary = {
      totalAnalyzed: analyses.length,
      averageScore: analyses.reduce((sum, a) => sum + a.qualityScore, 0) / analyses.length,
      needingImprovement: analyses.filter(a => a.qualityScore < 70).length,
      timestamp: new Date().toISOString(),
      analyses: analyses.map(a => ({
        productId: a.productId,
        score: a.qualityScore,
        issues: a.suggestedImprovements.length,
      })),
    };
    
    console.log('💾 Saving analysis results...');
    
    // For now, we'll log to console. In a full implementation, 
    // you might want to create an ImageAnalysisLog model
    console.log('📊 Analysis Summary:', JSON.stringify(summary, null, 2));
    
  } catch (error) {
    console.error('❌ Failed to save analysis results:', error);
  }
}

// Execute the script
if (require.main === module) {
  main()
    .catch((error) => {
      console.error('❌ Script execution failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
      console.log('\n👋 Script finished - database disconnected');
    });
}

export { analyzeProductImages };
export type { ImageAnalysis, ImageSuggestion };