import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";

// Initialize Gemini AI client with API key
function getGeminiClient() {
  const googleApiKey = process.env.GOOGLE_AI_API_KEY;
  if (!googleApiKey) {
    throw new Error("GOOGLE_AI_API_KEY environment variable is not set");
  }
  return new GoogleGenerativeAI(googleApiKey);
}

/**
 * Fetches an image from a URL and returns it as base64 data.
 */
async function fetchImageAsBase64(url: string): Promise<{ base64: string, mimeType: string }> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  if (!contentType?.startsWith('image/')) {
    throw new Error('Invalid content type: Must be an image');
  }

  const arrayBuffer = await response.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');
  
  return { base64, mimeType: contentType };
}

/**
 * Processes either a base64 string or URL into the format needed for Gemini API
 */
async function processImage(imageInput: string): Promise<{ base64: string, mimeType: string }> {
  // Check if the input is a URL
  try {
    const url = new URL(imageInput);
    if (url.protocol === 'http:' || url.protocol === 'https:') {
      return await fetchImageAsBase64(imageInput);
    }
  } catch (e) {
    // Not a URL, assume it's base64
  }

  // Handle base64 input
  const base64Data = imageInput.replace(/^data:image\/\w+;base64,/, '');
  const mimeType = imageInput.match(/^data:([^;]+);/)?.[1] || 'image/jpeg';
  
  if (!mimeType.startsWith('image/')) {
    throw new Error('Invalid content type: Must be an image');
  }
  
  return { base64: base64Data, mimeType };
}

/**
 * Calls Gemini Vision API to generate a product description from a base64 encoded image.
 * Accepts an optional category parameter to apply category-specific rules.
 * Supports different analysis types for various use cases.
 * This function must only be used in server-side code (API routes, getServerSideProps, etc.)
 */
export async function getProductDescriptionFromImage(
  base64Image: string, 
  category?: string,
  analysisType: 'product_description' | 'quality_and_angles' = 'product_description'
): Promise<string | null> {
  try {
    if (typeof window !== "undefined") {
      throw new Error("This function can only be executed server-side");
    }
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    const { base64, mimeType } = await processImage(base64Image);

    // Choose prompt based on analysis type
    let prompt: string[];
    
    if (analysisType === 'quality_and_angles') {
      prompt = [
        "You are an expert image quality analyst for e-commerce product photography. Analyze this product image and provide detailed quality assessment.",
        "",
        "ANALYSIS REQUIREMENTS:",
        "1. IMAGE QUALITY ASSESSMENT:",
        "   - clarity: 'high' | 'medium' | 'low'",
        "   - lighting: 'excellent' | 'good' | 'fair' | 'poor'",
        "   - background: 'clean' | 'neutral' | 'cluttered' | 'distracting'",
        "   - resolution: 'high' | 'medium' | 'low'",
        "   - focus: 'sharp' | 'acceptable' | 'blurry'",
        "",
        "2. PRODUCT VISIBILITY:",
        "   - angle: 'front' | 'side' | 'back' | 'three-quarter' | 'top' | 'bottom' | 'detail'",
        "   - coverage: percentage of product visible (0-100)",
        "   - brandVisible: true | false",
        "   - detailsVisible: true | false",
        "",
        "3. MISSING ELEMENTS:",
        "   - missingAngles: array of angles that would improve the listing",
        "   - suggestedShots: array of additional shots needed",
        "",
        "4. PRODUCT IDENTIFICATION:",
        "   - brand: detected brand name",
        "   - model: detected model/product name",
        "   - colorway: detected color scheme",
        "   - category: product category",
        "",
        "5. RECOMMENDATIONS:",
        "   - qualityImprovements: array of specific improvements needed",
        "   - priorityLevel: 'low' | 'medium' | 'high' | 'critical'",
        "",
        "Return a JSON object with all these fields. Be specific and actionable in recommendations.",
      ];
    } else {
      // Original product description prompt
      prompt = [
      "You are an expert product analyst and copywriter for a premium e-commerce store. Your job is to analyze product images and generate highly accurate, compelling, and category-specific product listings.",
      "",
      "1. CATEGORY DETECTION:",
      "- If a category is provided, use it. Otherwise, infer the category from the image (e.g., Sneakers, Clothing, Electronics, Accessories, etc.).",
      `- Category: ${category || '[Detect from image]'}`,
      "",
      "2. CATEGORY-SPECIFIC RULES:",
      "- For Sneakers: Analyze brand, model, colorway, gender, sizing, and premium features. Use official names and colorways. Highlight comfort, style, and technology. Suggest price in Maloti (M).",
      "- For Clothing: Identify type (e.g., T-shirt, hoodie), brand, material, color, fit (men/women/unisex), and notable design features. Suggest price in Maloti (M).",
      "- For Electronics: Identify product type (e.g., headphones, phone), brand, model, key specs, and unique selling points. Suggest price in Maloti (M).",
      "- For Accessories: Identify type (e.g., bag, watch), brand, material, color, and style. Suggest price in Maloti (M).",
      "- For all categories: Use a positive, premium tone. Never mention flaws or condition. Always return a structured JSON object.",
      "",
      "3. OUTPUT FORMAT:",
      "Return a JSON object with these keys:",
      "- name: Full product name (brand + model/type + key features)",
      "- brand: Brand name",
      "- category: Product category",
      "- description: 2-3 sentences, benefit-focused, premium, and category-specific",
      "- gender: (if applicable: Men's, Women's, Unisex, or omit if not relevant)",
      "- sizes: (if applicable: array of available sizes, or omit if not relevant)",
      "- stock: Random number between 10-50",
      "- price: Suggested price in Maloti (M)",
      "- discountedPrice: Discounted price if relevant, else omit",
      "",
      "4. EXAMPLES:",
      "For Sneakers: {\"name\":\"Nike Air Max 90 Black/White\",\"brand\":\"Nike\",\"category\":\"Sneakers\",\"description\":\"Experience iconic comfort and style...\",\"gender\":\"Men's\",\"sizes\":[\"6\",\"7\",\"8\"],\"stock\":32,\"price\":2200,\"discountedPrice\":1800}",
      "For Clothing: {\"name\":\"Adidas Originals Hoodie Black\",\"brand\":\"Adidas\",\"category\":\"Clothing\",\"description\":\"Stay cozy in this premium cotton hoodie...\",\"gender\":\"Unisex\",\"sizes\":[\"S\",\"M\",\"L\"],\"stock\":20,\"price\":900}",
      "For Electronics: {\"name\":\"Apple AirPods Pro (2nd Gen)\",\"brand\":\"Apple\",\"category\":\"Electronics\",\"description\":\"Enjoy immersive sound with...\",\"stock\":15,\"price\":3500}",
      "For Accessories: {\"name\":\"Fossil Leather Wallet Brown\",\"brand\":\"Fossil\",\"category\":\"Accessories\",\"description\":\"Crafted from genuine leather...\",\"stock\":40,\"price\":400}",
      "",
      "5. CRITICAL GUIDELINES:",
      "- Be rigorous and specific for each category.",
      "- Never mention flaws, condition, or negative aspects.",
      "- Use official names, specs, and colorways where possible.",
      "- Always return valid JSON."
      ];
    }
    
    const promptText = prompt.join("\n");

    const result = await model.generateContent([
      { text: promptText },
      {
        inlineData: {
          data: base64,
          mimeType
        }
      }
    ]);
    const response = await result.response;
    const text = response.text();
    if (!text) {
      throw new Error("Gemini API returned empty response");
    }
    // Try to extract JSON from the response
    let jsonStart = text.indexOf('{');
    let jsonEnd = text.lastIndexOf('}');
    let jsonString = (jsonStart !== -1 && jsonEnd !== -1) ? text.substring(jsonStart, jsonEnd + 1) : text;
    try {
      JSON.parse(jsonString);
    } catch {
      // fallback: try to fix common issues
      jsonString = text.replace(/^[^\{]*/, '').replace(/[^\}]*$/, '');
    }
    return jsonString;
  } catch (error) {
    console.error("Gemini API error:", error);
    return null;
  }
}

/**
 * Analyze image quality and angles specifically
 */
export async function analyzeImageQuality(imageInput: string): Promise<any> {
  return getProductDescriptionFromImage(imageInput, undefined, 'quality_and_angles');
}

/**
 * Batch analyze multiple images
 */
export async function batchAnalyzeImages(
  images: Array<{ url: string; productId: string; category?: string }>,
  analysisType: 'product_description' | 'quality_and_angles' = 'product_description'
): Promise<Array<{ productId: string; result: any; error?: string }>> {
  const results = [];
  
  for (const image of images) {
    try {
      const result = await getProductDescriptionFromImage(image.url, image.category, analysisType);
      results.push({ productId: image.productId, result });
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      results.push({ 
        productId: image.productId, 
        result: null, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
  
  return results;
}
