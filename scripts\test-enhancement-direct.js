#!/usr/bin/env node

/**
 * Direct Image Enhancement Test Script
 * This script tests the image enhancement pipeline directly without API authentication
 */

const { enhanceProductImages } = require('../utils/advanced-image-processor.ts');
const { PrismaClient } = require('@prisma/client');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

async function testDirectEnhancement() {
  console.log('🧪 Direct Image Enhancement Test');
  console.log('================================');

  try {
    // Get a test product
    const product = await prisma.product.findFirst({
      where: {
        images: {
          isEmpty: false
        }
      },
      select: {
        id: true,
        name: true,
        brand: true,
        images: true
      }
    });

    if (!product) {
      console.error('❌ No products found with images');
      return;
    }

    console.log(`🎯 Testing with product: ${product.name} (${product.brand})`);
    console.log(`📸 Original images: ${product.images.length}`);
    console.log(`🔗 First image URL: ${product.images[0]}`);

    // Test the enhancement
    console.log('\n🚀 Starting enhancement...');
    const result = await enhanceProductImages(product.images[0], product.id);

    console.log('\n✅ Enhancement completed!');
    console.log('📊 Results:');
    console.log(`   Confidence Score: ${result.confidence_score}%`);
    console.log(`   Images Found: ${result.images.length}`);
    console.log(`   Image Source: ${result.image_source}`);
    console.log(`   Processing Notes: ${result.processing_notes.length} notes`);

    // Show processing notes
    console.log('\n📝 Processing Notes:');
    result.processing_notes.forEach((note, index) => {
      console.log(`   ${index + 1}. ${note}`);
    });

    // Show image details
    console.log('\n🖼️ Enhanced Images:');
    result.images.forEach((img, index) => {
      console.log(`   ${index + 1}. ${img.url}`);
      console.log(`      Angle: ${img.angle}`);
      console.log(`      Quality: ${img.qualityScore}%`);
      console.log(`      Source: ${img.source}`);
    });

    // Update the product in database
    if (result.confidence_score >= 50) {
      console.log('\n💾 Updating product in database...');
      await prisma.product.update({
        where: { id: product.id },
        data: {
          images: result.images.map(img => img.url),
          enhancementStatus: result.confidence_score >= 85 ? 'completed' : 'review_required',
          qualityScore: result.confidence_score
        }
      });
      console.log('✅ Product updated successfully');
    } else {
      console.log('⚠️ Low confidence score, not updating product');
    }

  } catch (error) {
    console.error('❌ Enhancement failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDirectEnhancement().catch(console.error);
